{% extends "base.html" %}

{% block title %}MCQ Text Extractor{% endblock %}
{% block content %}
<!-- KaTeX CSS and JavaScript -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.11/dist/katex.min.css" integrity="sha384-nB0miv6/jRmo5UMMR1wu3Gz6NLsoTkbqJghGIsx//Rlm+ZU03BU6SQNC66uf4l5+" crossorigin="anonymous">
<script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.11/dist/katex.min.js" integrity="sha384-7zkQWkzuo3B5mTepMUcHkMB5jZaolc2xDwL6VFqjFALcbeS9Ggm/Yr2r3Dy4lfFg" crossorigin="anonymous"></script>
<script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.11/dist/contrib/auto-render.min.js" integrity="sha384-43gviWU0YVjaDtb/GhzOouOXtZMP/7XUzwPTstBeZFe/+rCMvRwr4yROQP43s0Xk" crossorigin="anonymous"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-browser/0.1.0/jquery.browser.min.js" defer></script>
<style>
    .extraction-summary {
        background-color: #f5f5f5;
        padding: 15px;
        border-radius: 5px;
        margin-bottom: 20px;
    }

    .text-content {
        margin-top: 20px;
        overflow-y: auto;
        border: 1px solid #ddd;
        border-radius: 5px;
        padding: 15px;
        background-color: white;
        font-family: monospace;
        white-space: pre-wrap;
        word-wrap: break-word;
    }

    pre{
        white-space: break-spaces !important;
    }

    .spinner {
        display: none;
        border: 4px solid #f3f3f3;
        border-top: 4px solid #3498db;
        border-radius: 50%;
        width: 30px;
        height: 30px;
        animation: spin 1s linear infinite;
        margin: 20px auto;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .timer-display {
        text-align: center;
        margin: 15px 0;
        padding: 10px;
        background-color: #e3f2fd;
        border: 1px solid #2196f3;
        border-radius: 5px;
        color: #1976d2;
        font-size: 16px;
    }

    .progress-log {
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 5px;
        padding: 15px;
        margin: 20px 0;
        max-height: 150px;
        overflow-y: auto;
        font-family: monospace;
        font-size: 12px;
        line-height: 1.4;
    }

    .progress-log .log-entry {
        margin-bottom: 5px;
        padding: 2px 0;
    }

    .progress-log .log-entry.info {
        color: #007bff;
    }

    .progress-log .log-entry.success {
        color: #28a745;
        font-weight: bold;
    }

    .progress-log .log-entry.error {
        color: #dc3545;
        font-weight: bold;
    }

    .progress-log .log-entry.warning {
        color: #ffc107;
    }

    .copy-button {
        background-color: #007bff;
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 4px;
        cursor: pointer;
        margin-bottom: 10px;
        font-size: 14px;
    }

    .copy-button:hover {
        background-color: #0056b3;
    }

    .copy-button:active {
        background-color: #004085;
    }

    .success-message {
        background-color: #d4edda;
        color: #155724;
        padding: 10px;
        border-radius: 5px;
        margin-bottom: 15px;
        border: 1px solid #c3e6cb;
        overflow-x: scroll;
    }

    .error-message {
        background-color: #f8d7da;
        color: #721c24;
        padding: 10px;
        border-radius: 5px;
        margin-bottom: 15px;
        border: 1px solid #f5c6cb;
    }

    .view-text-button {
        background-color: #28a745;
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 4px;
        cursor: pointer;
        margin-left: 10px;
        font-size: 14px;
    }

    .view-text-button:hover {
        background-color: #218838;
    }
    #forceReextract{
        margin-bottom: 0 !important;
    }
</style>

<div class="test-container">
    <div class="test-card">
        <h1>MCQ Text Extractor</h1>
        <p class="test-description">Enter a resource ID to extract text content from MCQ images</p>

        <form id="textExtractorForm" class="login-form">
            <label for="resId">Enter Res ID</label>
            <input type="text" id="resId" name="resId" required>

            <label for="totalQuestions">Total Number of Questions</label>
            <input type="number" id="totalQuestions" name="totalQuestions" min="1" required>

            <label for="explanationStartPage">Explanation Start Page Number</label>
            <input type="number" id="explanationStartPage" name="explanationStartPage" min="1" required>

            <div style="margin: 10px 0;">
                <label style="display: flex; align-items: center; font-size: 14px;">
                    <input type="checkbox" id="forceReextract" name="forceReextract" style="margin-right: 8px;">
                    Force re-extraction (even if file already exists)
                </label>
            </div>

            <button type="submit">Extract Text</button>
        </form>
        <div id="spinner" class="spinner"></div>
        <div id="timerDisplay" class="timer-display" style="display: none;">
            <strong>Elapsed Time: <span id="timerValue">00:00</span></strong>
        </div>
        <div id="progressLog" class="progress-log" style="display: none;"></div>

        <div id="resultsContainer" style="display: none; margin-top: 20px;">
            <h3>Extraction Results:</h3>
            <div id="extractionSummary" class="extraction-summary"></div>
            <div id="textContent" class="text-content"></div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Get the base URL from the current location
const BASE_URL = window.location.origin;
console.log('BASE_URL:', BASE_URL);
const form = document.getElementById('textExtractorForm');
const resultsContainer = document.getElementById('resultsContainer');
const extractionSummary = document.getElementById('extractionSummary');
const textContent = document.getElementById('textContent');
const spinner = document.getElementById('spinner');
const progressLog = document.getElementById('progressLog');
const timerDisplay = document.getElementById('timerDisplay');
const timerValue = document.getElementById('timerValue');

// Timer variables
let startTime = null;
let timerInterval = null;

// Timer functions
function startTimer() {
    startTime = Date.now();
    timerDisplay.style.display = 'block';
    timerValue.textContent = '00:00';

    timerInterval = setInterval(() => {
        const elapsed = Date.now() - startTime;
        const minutes = Math.floor(elapsed / 60000);
        const seconds = Math.floor((elapsed % 60000) / 1000);
        timerValue.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }, 1000);
}

function stopTimer() {
    if (timerInterval) {
        clearInterval(timerInterval);
        timerInterval = null;
    }

    if (startTime) {
        const elapsed = Date.now() - startTime;
        const minutes = Math.floor(elapsed / 60000);
        const seconds = Math.floor((elapsed % 60000) / 1000);
        const finalTime = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        timerValue.textContent = finalTime;
        return finalTime;
    }
    return '00:00';
}

function resetTimer() {
    stopTimer();
    timerDisplay.style.display = 'none';
    startTime = null;
}

form.addEventListener('submit', async (e) => {
    e.preventDefault();

    const formData = new FormData(form);
    const resId = formData.get('resId');
    const totalQuestions = parseInt(formData.get('totalQuestions'));
    const explanationStartPage = parseInt(formData.get('explanationStartPage'));
    const forceReextract = formData.get('forceReextract') === 'on';

    if (!resId) {
        alert('Please enter a resource ID');
        return;
    }

    if (!totalQuestions || totalQuestions < 1) {
        alert('Please enter a valid total number of questions (minimum 1)');
        return;
    }

    if (!explanationStartPage || explanationStartPage < 1) {
        alert('Please enter a valid explanation start page number (minimum 1)');
        return;
    }

    // Reset any previous timer and show spinner and progress log
    resetTimer();
    spinner.style.display = 'block';
    progressLog.style.display = 'block';
    progressLog.innerHTML = '';
    resultsContainer.style.display = 'none';

    // Start the timer
    startTimer();

    // Add initial log entry
    const extractionType = forceReextract ? 'forced MCQ text extraction' : 'MCQ text extraction';
    addLogEntry(`Starting ${extractionType}...`, 'info');

    try {
        const response = await fetch(`${BASE_URL}/api/mcq-text-extractor`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                resId: resId,
                force_reextract: forceReextract,
                total_questions: totalQuestions,
                explanation_start_page: explanationStartPage
            })
        });

        const result = await response.json();

        if (result.status === 'started') {
            // Task started successfully, begin polling
            addLogEntry(`Task started with ID: ${result.task_id}`, 'info');
            addLogEntry('Polling for status updates...', 'info');

            // Start polling for status
            pollTaskStatus(result.task_id);
        } else {
            // Stop timer and get final time
            const finalTime = stopTimer();

            // Hide spinner
            spinner.style.display = 'none';

            addLogEntry(`Error: ${result.message} (Failed after ${finalTime})`, 'error');
            displayError(result.message);
        }
        
    } catch (error) {
        console.error('Error:', error);
        const finalTime = stopTimer();
        spinner.style.display = 'none';
        addLogEntry(`Network error: ${error.message} (Failed after ${finalTime})`, 'error');
        displayError('Network error occurred. Please try again.');
    }
});

function addLogEntry(message, type = 'info') {
    const timestamp = new Date().toLocaleTimeString();
    const logEntry = document.createElement('div');
    logEntry.className = `log-entry ${type}`;
    logEntry.textContent = `[${timestamp}] ${message}`;
    progressLog.appendChild(logEntry);
    progressLog.scrollTop = progressLog.scrollHeight;
}

function displayExtractionResults(result, elapsedTime = '00:00') {
    // Show results container
    resultsContainer.style.display = 'block';

    // Display summary based on whether file already existed or was newly extracted
    const title = result.already_existed ? "File Already Exists!" : "Extraction Successful!";
    const statusMessage = result.already_existed ?
        "Text file was already available in S3" :
        `Successfully extracted text from ${result.total_images} images`;

    extractionSummary.innerHTML = `
        <div class="success-message">
            <strong>${title}</strong><br>
            ${statusMessage}<br>
            <strong>Time Taken: ${elapsedTime}</strong><br>
            ${result.total_images > 0 ? `Total Images Processed: ${result.total_images}<br>` : ''}
            ${result.text_files_created > 0 ? `Text Files Created: ${result.text_files_created}<br>` : ''}
            Chapter ID: ${result.chapter_id}<br>
            Resource ID: ${result.resource_id}<br>
            S3 Path: ${result.s3_path}
        </div>
        <button class="view-text-button" onclick="loadExtractedText('${result.chapter_id}', '${result.resource_id}')">
            View Extracted Text
        </button>
    `;

    // Clear text content
    textContent.innerHTML = '';
}

function displayError(message) {
    resultsContainer.style.display = 'block';
    extractionSummary.innerHTML = `
        <div class="error-message">
            <strong>Extraction Failed!</strong><br>
            ${message}
        </div>
    `;
    textContent.innerHTML = '';
}

// Polling function for task status
async function pollTaskStatus(taskId) {
    const pollInterval = 5000; // 5 seconds
    let pollCount = 0;
    const maxPolls = 360; // Maximum 30 minutes (360 * 5 seconds)

    const poll = async () => {
        try {
            pollCount++;
            addLogEntry(`Checking status...`, 'info');

            const response = await fetch(`${BASE_URL}/api/mcq-text-extractor/status`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    task_id: taskId
                })
            });

            const statusResult = await response.json();

            if (statusResult.status === 'COMPLETED') {
                // Task completed successfully
                const finalTime = stopTimer();
                spinner.style.display = 'none';

                addLogEntry(`Text extraction completed successfully! (Completed in ${finalTime})`, 'success');

                // Display results from the task result
                if (statusResult.result) {
                    displayExtractionResults(statusResult.result, finalTime);
                } else {
                    addLogEntry('Task completed but no result data available', 'warning');
                }

                return; // Stop polling

            } else if (statusResult.status === 'FAILED') {
                // Task failed
                const finalTime = stopTimer();
                spinner.style.display = 'none';

                const errorMsg = statusResult.error_message || 'Unknown error occurred';
                addLogEntry(`Error: ${errorMsg} (Failed after ${finalTime})`, 'error');
                displayError(errorMsg);

                return; // Stop polling

            } else if (statusResult.status === 'not_found') {
                // Task not found
                const finalTime = stopTimer();
                spinner.style.display = 'none';

                addLogEntry(`Error: Task not found (Failed after ${finalTime})`, 'error');
                displayError('Task not found');

                return; // Stop polling

            } else if (statusResult.status === 'IN_PROGRESS') {
                addLogEntry('Task is in progress...', 'info');

            } else if (statusResult.status === 'STARTED') {
                addLogEntry('Task is starting...', 'info');
            }

            // Continue polling if not completed/failed and under max polls
            if (pollCount < maxPolls) {
                setTimeout(poll, pollInterval);
            } else {
                // Max polls reached
                const finalTime = stopTimer();
                spinner.style.display = 'none';

                addLogEntry(`Polling timeout reached (${finalTime})`, 'error');
                displayError('Task is taking too long. Please check back later.');
            }

        } catch (error) {
            console.error('Error polling task status:', error);
            addLogEntry(`Error checking status: ${error.message}`, 'error');

            // Continue polling on error (might be temporary network issue)
            if (pollCount < maxPolls) {
                setTimeout(poll, pollInterval);
            } else {
                const finalTime = stopTimer();
                spinner.style.display = 'none';
                displayError('Failed to check task status');
            }
        }
    };

    // Start polling after a short delay
    setTimeout(poll, 2000); // Wait 2 seconds before first poll
}

async function loadExtractedText(chapterId, resId) {
    try {
        addLogEntry('Loading extracted text...', 'info');
        
        const response = await fetch(`${BASE_URL}/api/get-mcq-text/${chapterId}/${resId}`);
        const result = await response.json();
        
        if (result.status === 'success') {
            addLogEntry('Text loaded successfully!', 'success');
            displayTextContent(result.content);
        } else {
            addLogEntry(`Error loading text: ${result.message}`, 'error');
            textContent.innerHTML = `<div class="error-message">Error loading text: ${result.message}</div>`;
        }
        
    } catch (error) {
        console.error('Error loading text:', error);
        addLogEntry(`Error loading text: ${error.message}`, 'error');
        textContent.innerHTML = `<div class="error-message">Error loading text: ${error.message}</div>`;
    }
}

function displayTextContent(content) {
    textContent.innerHTML = `
        <button class="copy-button" onclick="copyTextToClipboard()">Copy Text</button>
        <pre id="textContentData">${content}</pre>
    `;
    renderMathInElement(textContent, {
        delimiters: [
            {left: '$$', right: '$$', display: true},
            {left: '$', right: '$', display: false},
            {left: '\\(', right: '\\)', display: false},
            {left: '\\[', right: '\\]', display: true}
        ],
        throwOnError: false,
        output: 'html',
        ignoredTags: []
    });
}

function copyTextToClipboard() {
    const textData = document.getElementById('textContentData').textContent;
    navigator.clipboard.writeText(textData).then(() => {
        addLogEntry('Text copied to clipboard!', 'success');
    }).catch(err => {
        console.error('Failed to copy text: ', err);
        addLogEntry('Failed to copy text to clipboard', 'error');
    });
}

</script>
{% endblock %}
